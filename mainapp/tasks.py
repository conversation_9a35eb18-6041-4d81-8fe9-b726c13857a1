import concurrent.futures
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import time
import logging
import urllib.error
import asyncio
import hashlib
import re
import datetime
import asyncio
import uuid
from typing import List, Dict
from urllib.request import urlopen
from zoneinfo import ZoneInfo

import stripe
import requests
from celery import shared_task
from celery_progress.backend import ProgressRecorder
from zoneinfo import ZoneInfo
from django.core.files import File
from django.core.files.temp import NamedTemporaryFile
from django.db import IntegrityError, transaction
import os
import copy

from AbunDRFBackend.settings import FLY_API_HOST, DEBUG
from mainapp.models import (KubernetesJob, Logo, HypestatData, User, GPT4UsageStats, Website, GlossaryTopic, GlossaryContent,
                            GuestPostFinderQuery, RedditPostFinderQuery, Article, WebsiteScanQueue, WordpressIntegration, WordpressPublishedArticle, ProgrammaticSeoTitle, KeywordProject, Keyword, BlockKeywords)
from mainapp.utils import (chunker, get_guest_post_data, get_glossary_contents_using_llm, unescape_amp_char,
                           get_reddit_post_data, get_glossary_internal_link_using_llm, glossary_fetch_internal_links, glossary_add_internal_links,
                           remove_repeated_line, create_article_from_wp_post, save_wordpress_published_article,
                           fetch_and_create_featured_image, fetch_published_wp_posts, get_programmatic_seo_using_llm, generate_article_uid, get_glossary_words_using_llm, get_keywords_from_domain, get_keyword_volume_data, create_keyword_object, get_keywords_using_llm)
from mainapp.article_title_gen_v2.titles_gen_v2 import ArticleTitleGeneratorV2
from mainapp.stripe_utils import get_stripe_product_data
from mainapp.website_scanning import WebsiteScanning
from mainapp import google_integration_utils

logger = logging.getLogger('abun.backend.celery')

"""
--------------------------------------------------------------------------------------------------------
NOTE: Please use "celery_" prefix in function names so that it's easier to identify these calls in code.
--------------------------------------------------------------------------------------------------------
"""


@shared_task
def celery_save_logos(domain_list: List[str]):
    """
    Celery task to save logo images for a list of domains to our media storage (Cloudflare R2)

    :param domain_list: List of domains (ex. ['draftss.com', ...])
    """
    # Get all domains that are not present in Logo table
    new_domains: List[str] = list(
        set(domain_list) - set(Logo.objects.filter(domain__in=domain_list).values_list('domain', flat=True))
    )

    # For each domain, fetch the logo image from clearbit and save it to media storage.
    for domain in new_domains:
        if not Logo.objects.filter(domain=domain).exists():
            try:
                img_temp = NamedTemporaryFile(delete=True)
                img_temp.write(urlopen(f"https://logo.clearbit.com/{domain}").read())
                img_temp.flush()

                logo = Logo(domain=domain)
                logo.image.save(domain, File(img_temp))

                logo.save()
            except urllib.error.HTTPError:
                # skip
                logger.error(f"HTTPError: Failed fetching logo for {domain} from clearbit.")

            except Exception as err:
                # skip
                logger.error(f"Exception: Failed saving logo for {domain}: {err}")


@shared_task
def celery_save_hypestat_data(domain_list: List[str]):
    """
    Fetch and save hypestat data for :param domain_list in parallel.
    :param domain_list:
    :return:
    """
    # Get all domains that are not present in HypestatData table
    new_domains: List[str] = list(
        set(domain_list) - set(HypestatData.objects.filter(domain__in=domain_list).values_list('domain', flat=True))
    )

    # For each domain, fetch the data from hypestat and save it to db table in parallel using ThreadPoolExecutor.
    with concurrent.futures.ThreadPoolExecutor(max_workers=100) as executor:
        futures = []
        for domains in chunker(new_domains, 3):
            futures.append(executor.submit(fetch_hypestat_data, domains))

        # Wait for all tasks to complete
        results: List[Dict] = []
        for future in concurrent.futures.as_completed(futures):
            results.extend(future.result())

        # Save all data
        for hypestat_data in results:
            try:
                hype = HypestatData(
                    domain=hypestat_data['domain'],
                    organic_traffic=hypestat_data['organic_traffic'],
                    organic_keywords=hypestat_data['organic_keywords'],
                    domain_authority=hypestat_data['domain_authority'],
                    total_backlinks=hypestat_data['total_backlinks'],
                    follow=hypestat_data['follow'],
                    no_follow=hypestat_data['no_follow'],
                    referring_domains=hypestat_data['referring_domains'],
                )
                hype.save()
            except IntegrityError as err:
                print(f"[*] Could not save hypestat data for {hypestat_data['domain']}: {err}")


def fetch_hypestat_data(domains: List[str]):
    results: List[Dict] = []
    for domain in domains:
        res = requests.get(f"https://wjoazptcqycbxodkxmgkgr2yb40qrfpz.lambda-url.us-east-1.on.aws/?domain={domain}")
        if res.status_code == 200:
            res_json = res.json()
            results.append({
                "domain": domain,
                "organic_traffic": res_json['organic_traffic'],
                "organic_keywords": res_json['organic_keywords'],
                "domain_authority": res_json['domain_authority'],
                "total_backlinks": res_json['total_backlinks'],
                "follow": res_json['follow'],
                "no_follow": res_json['no_follow'],
                "referring_domains": res_json['referring_domains'],
            })
        else:
            print(f"[*] Could not fetch hypestat data for {res.status_code}")
            results.append({
                "domain": domain,
                "organic_traffic": None,
                "organic_keywords": None,
                "domain_authority": None,
                "total_backlinks": None,
                "follow": None,
                "no_follow": None,
                "referring_domains": None,
            })

    return results


@shared_task(bind=True) # Bind the task to allow self to access the task instance
def celery_generate_titles(self, email, keyword_hash, location="us", title_count=10, type_of_generation="default"):
    progress_recorder = ProgressRecorder(self)

    try:
        user: User = User.objects.filter(email=email).first()
        generator = ArticleTitleGeneratorV2(user)
        progress_recorder.set_progress(20, 100, description="Preparing inputs")

        prepared_data = generator.prepare_inputs(keyword_hash, location, title_count)
        progress_recorder.set_progress(30, 100, description="Inputs prepared")

        stop_faking_progress = False

        async def fake_progress():
            for i in range(30, 90, 5):
                if stop_faking_progress:
                    break
                progress_recorder.set_progress(i, 100, description="Generating titles")
                await asyncio.sleep(1)

        # Create an asyncio event loop and run tasks concurrently
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # faking progress asynchrnously to show progress bar while GPT is generating titles, because there is no way to
        # quantify the progress of GPT while it's generating titles, we might improve this in future
        fake_progress_task = loop.create_task(fake_progress())  # Run fake_progress asynchronously

        # Generate titles asynchronously
        result = loop.run_until_complete(generator._generate_titles_async(prepared_data['inputs']))

        # Mark fake progress as stopped
        stop_faking_progress = True
        loop.run_until_complete(fake_progress_task)  # Ensure fake_progress finishes if not already done
        loop.close()
        results = result['results']
        logger.debug(f"[*] Article Title Generation: Generated {len(results)} titles")

        progress_recorder.set_progress(95, 100, description="Titles generated. Saving titles")

        # before saving check if the user has reached the max limit of titles allowed
        max_titles_allowed: int = get_stripe_product_data(user)['metadata']['max_titles']

        if (user.titles_generated + len(results)) > max_titles_allowed:
            return f"Error: You have reached the maximum limit of titles allowed for your subscription. Please upgrade your subscription to generate more titles."

        # Save the generated titles to the database
        list_of_titles = generator.save_titles(
            results,
            prepared_data['serper_results'],
            prepared_data['keyword_object'],
            prepared_data['other_top_ranking_urls'],
            type_of_generation
        )

        # Save GPT-4 usage stats
        GPT4UsageStats.objects.create(
            user=user,
            usage_type="title_generation",
            usage_cost=result['cost'],
        )

        if not isinstance(list_of_titles, str):
            titles_generated = len(list_of_titles)
            user.titles_generated += titles_generated
            user.total_titles_generated += titles_generated
            user.save()

        progress_recorder.set_progress(100, 100, description="Titles saved")
        loop.close()

        # Return the list of titles
        return list_of_titles
    except Exception as e:
        logger.error(f"Error in Celery task generating titles: {e}")
        return "Error in generating titles: " + str(e)


@shared_task
def celery_start_website_scanning(website_domain: str,
                                  sitemap_url: str | List[str] = [],
                                  website_urls: List = [],
                                  rescrape: bool = False,
                                  request_from_admin: bool = False,
                                  tool_to_run: str = 'generate-summary'):
    """
    Celery task to scrape and store the website pages
    :param website_domain: Website domain
    :param sitemap_url: Sitemap URL or list of sitemap URLs
    :param website_urls: List of website URLs
    :param rescrape: Re-Scrape the website
    :param request_from_admin: Whether request is from admin
    :param tool_to_run: Which tool to run - 'generate-schema', 'generate-summary', 'generate-seo-data', 'all'
    """
    # Get the website instance
    try:
        website = Website.objects.get(domain=website_domain)
    except Website.DoesNotExist:
        logger.error(f"No website found with '{website_domain}' domain")
        return None

    if DEBUG:
        # Start scanning the website
        websiteScanning = WebsiteScanning(
            website,
            sitemap_url,
            website_urls,
            admin_request=request_from_admin,
            tool_to_run=tool_to_run)

        if not rescrape:
            print("[*] Running website scanning on dev/staging environment...")
            websiteScanning.run()
            print("[*] Website scanning completed on dev/staging environment.")
        else:
            websiteScanning.delete_and_run()
    else:
        # Check if the website scanning tasks is already in queue
        if WebsiteScanQueue.objects.filter(website=website, status__in=['queued', 'processing']).exists():
            logger.error("Website scanning task is already queued. Discarding request...")
            return None

        # Queue the website scanning task
        WebsiteScanQueue.objects.create(
            website=website,
            sitemap_urls=isinstance(sitemap_url, str) and [sitemap_url] or sitemap_url,
            website_urls=website_urls,
            request_from_admin=request_from_admin,
            tool_to_run=tool_to_run
        )
        website.task_queued = True
        website.save()


@shared_task
def generate_bulk_glossary_content_task(user_id, project_id, glossary_words):
    try:
        user = User.objects.get(id=user_id)
        website: Website = user.current_active_website
        language = user.article_language_preference
        count = website.max_internal_glossary_backlinks

        product_data = get_stripe_product_data(user)
        max_terms_allowed = max(0, product_data['metadata']['max_glossary_words'] - user.glossary_contents_generated)

        glossary_topic = GlossaryTopic.objects.filter(project_id=project_id).first()
        if not glossary_topic:
            return {"status": "error", "message": "No glossary topics found for the given project_id."}

        topic = glossary_topic.word
        if glossary_words:
            terms = glossary_words
        else:
            # Get all terms already used in GlossaryContent for the same project_id
            existing_terms = GlossaryContent.objects.filter(
                project_id=glossary_topic.project_id
            ).values_list('term', flat=True)

            # Filter only those glossary words which are not already used
            terms = [term for term in glossary_topic.glossary_words if term not in existing_terms]

        terms_to_process = terms[:max_terms_allowed]  # Respect Stripe limits

        if not topic or not terms_to_process:
            return {"status": "error", "message": "No topic or glossary words available for the given project_id."}

        generated_contents = []
        failed_terms = []

        for term in terms_to_process:
            try:
                glossary_content = get_glossary_contents_using_llm(topic, term, language)
                extracted_content = glossary_content.get("content")
                content = remove_repeated_line(extracted_content, term)            
                if not content:
                    continue

                # Internal link generation
                if website.internal_glossary_backlinks_preference == "on":
                    try:
                        internal_links = get_glossary_internal_link_using_llm(term, content, count)
                        phrases = [phrase.strip() for phrase in internal_links.get("content", "").split(",") if phrase.strip()]

                        all_blog_links_data = []
                        for phrase in phrases:
                            try:
                                internal_links_data = glossary_fetch_internal_links(
                                    website_id=website.id,
                                    phrase=phrase,
                                    body_content=content
                                )
                                if internal_links_data and internal_links_data.get('internal_links'):
                                    all_blog_links_data.extend(internal_links_data['internal_links'])

                            except Exception as e:
                                logger.error(f"[Internal Link] Failed for phrase '{phrase}': {e}")

                        all_blog_links_data.sort(key=lambda d: d['similarity'], reverse=True)
                        content = glossary_add_internal_links(content, all_blog_links_data, term)

                    except Exception as e:
                        logger.error(f"[Internal Link Generation] Failed for term '{term}': {e}")

                # Clean em-dashes
                content = re.sub(r'\s*—\s*', ', ', content)

                # Unique hash logic
                keyword_hash = hashlib.md5(term.encode('utf-8')).hexdigest()
                while GlossaryContent.objects.filter(keyword_hash=keyword_hash).exists():
                    keyword_hash = hashlib.md5((term + str(user.id)).encode('utf-8')).hexdigest()

                glossary_instance = GlossaryContent.objects.create(
                    user=user,
                    project_id=project_id,
                    topic=unescape_amp_char(topic),
                    term=unescape_amp_char(term),
                    content=content,
                    keyword_hash=keyword_hash,
                    internal_link_count=0,
                    glossary_link=None,
                    is_processing=False,
                    is_failed=False,
                    is_generated=True,
                    is_posted=False,
                    is_archived=False,
                    posted_to='wordpress',
                    feedback='no_feedback',
                    website=user.current_active_website,
                    task_id=generate_bulk_glossary_content_task.request.id,
                )

                generated_contents.append({
                    "term": glossary_instance.term,
                    "keyword_hash": glossary_instance.keyword_hash,
                    "content": glossary_instance.content,
                })

            except Exception as e:
                logger.critical(f"[Glossary Generation] Failed for term '{term}': {e}")
                failed_terms.append({"term": term, "error": str(e)})

        # Final status
        if not generated_contents:
            return {"status": "rejected", "reason": "no_content_generated", "message": "No glossary content generated."}

        user.glossary_contents_generated += len(generated_contents)
        user.save()
        
        return {
            "status": "success",
            "generated_count": len(generated_contents),
            "generated_contents": generated_contents,
            "failed_terms": failed_terms
        }

    except Exception as e:
        logger.critical("[Task Failure] generate_bulk_glossary_content_task failed")
        return {"status": "error", "message": str(e)}


@shared_task
def celery_guest_post_finder(website_domain: str, query: str):
    """
    Celery task for guest post finder

    Parameters:
     - :param query: ex. the string to query
     - :param limit: How many questions to return?
     - :param stat: Hypestat boolean value

    Returns:
     - json response {title, link, domain_traffic ,domain_authority}
    """
    try:
      website = Website.objects.get(domain=website_domain)
      user = website.user
      guest_post_finder = GuestPostFinderQuery.objects.get(website__domain=website.domain, query=query)
    except Website.DoesNotExist as err:
        logger.error("Error in celery_guest_post_finder: %s", err)
        return


    # Serper API request headers and parameters
    headers = {
        "X-API-KEY": os.environ["GOOGLE_SERPER_API_KEY"],  # Add your Serper API key here
        "Content-Type": "application/json"
    }

    query_list = ["write for us", "submit an article", "submit blog post", "guest post"]
    user_query = guest_post_finder.query
    limit = guest_post_finder.limit
    total_titles_generated = 0
    # limit = max_gpf_queries_allowed

    for query in query_list:
        if limit != 0:
            reminder = int(limit / 100)
            modulo = limit % 100

            exact_query = f'"{query}"'

            if reminder != 0:
                url = f"https://google.serper.dev/search?q={user_query} inurl:{exact_query} intitle:{exact_query}&num=100"

                try:
                    response = requests.get(url, headers=headers, timeout=60)

                except requests.exceptions.ConnectionError as e:
                    logger.error("Connection error while requesting: %s", e)
                    continue

                titles_generated = get_guest_post_data(response, guest_post_finder, website)  # Process and save the results
                total_titles_generated += titles_generated
                limit = (reminder - 1) * 100 + modulo
            else:
                url = f"https://google.serper.dev/search?q={user_query} inurl:{exact_query} intitle:{exact_query}&num={modulo}"

                try:
                    response = requests.get(url, headers=headers, timeout=60)

                except requests.exceptions.ConnectionError as e:
                    logger.error("Connection error while requesting: %s", e)
                    continue

                titles_generated = get_guest_post_data(response, guest_post_finder, website)  # Process and save the results
                total_titles_generated += titles_generated
                limit = 0
        else:
            break

    guest_post_finder.is_processing = False
    guest_post_finder.limit = total_titles_generated
    guest_post_finder.save()
    user.guest_post_finder_queries_generated += total_titles_generated
    user.save()

@shared_task
def celery_reddit_post_finder(website_domain: str, query: str):
    """
    Celery task for reddit post finder

    Parameters:
     - :param query: ex. the string to query
     - :param limit: How many questions to return?
     - :param stat: Hypestat boolean value

    Returns:
     - json response {title, link, position}
    """
    try:
      website = Website.objects.get(domain=website_domain)
      user = website.user
      reddit_post_finder = RedditPostFinderQuery.objects.filter(website__domain=website.domain, query=query).latest('created_at')
    except Website.DoesNotExist as err:
        logger.error("Error in celery_reddit_post_finder: %s", err)
        return


    # Serper API request headers and parameters
    headers = {
        "X-API-KEY": os.environ["GOOGLE_SERPER_API_KEY"],  # Add your Serper API key here
        "Content-Type": "application/json"
    }

    user_query = reddit_post_finder.query
    limit = reddit_post_finder.limit
    total_titles_generated = 0

    if limit != 0:
            reminder = int(limit / 100)
            modulo = limit % 100

            exact_query = f'"{user_query}" site:reddit.com'

            if reminder != 0:
                url = f"https://google.serper.dev/search?q={exact_query}&num=100"

                try:
                    response = requests.get(url, headers=headers, timeout=60)

                except requests.exceptions.ConnectionError as e:
                    logger.error("Connection error while requesting: %s", e)

                titles_generated = get_reddit_post_data(response, reddit_post_finder)  # Process and save the results
                total_titles_generated += titles_generated
                limit = (reminder - 1) * 100 + modulo
            else:
                url =f"https://google.serper.dev/search?q={exact_query}&num={modulo}"

                try:
                    response = requests.get(url, headers=headers, timeout=60)

                except requests.exceptions.ConnectionError as e:
                    logger.error("Connection error while requesting: %s", e)

                titles_generated = get_reddit_post_data(response, reddit_post_finder)  # Process and save the results
                total_titles_generated += titles_generated
                limit = 0


    reddit_post_finder.is_processing = False
    reddit_post_finder.limit = total_titles_generated
    reddit_post_finder.save()
    user.reddit_post_finder_queries_generated += total_titles_generated
    user.save()


@shared_task
def celery_check_flyio_provisioning(machine_id: str,
                                    job_id: str,
                                    fly_app_name: str,
                                    fly_deploy_token: str):
    """
    Celery task to check the machine provisioning on flyio
    :param machine_id: Machine ID
    :param job_id: K8s Job ID
    :param fly_app_name: Fly.io app name
    :param fly_deploy_token: Fly.io deploy token
    """
    try:
        k8_job = KubernetesJob.objects.get(job_id=job_id)
    except KubernetesJob.DoesNotExist:
        logger.error(f"No K8 job exists with '{job_id}' Job ID.")
        return None

    max_retries = 3
    retry_delay = 5  # seconds

    for attempt in range(max_retries):
        res = requests.get(
            f"{FLY_API_HOST}/apps/{fly_app_name}/machines/{machine_id}/wait?state=started",
            headers={
                'Authorization': f"Bearer {fly_deploy_token}",
                'Content-Type': 'application/json'
            },
            timeout=120
        )

        if res.status_code == 200:
            break

        if attempt < max_retries - 1:
            logger.warning(f"Attempt {attempt + 1} failed. Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
    
    if res.status_code != 200:
        # Mark the k8 job as failed
        k8_job.status = "failed"
        k8_job.save()

        if k8_job.metadata.startswith("article"):
            article_uid = k8_job.metadata

            try:
                article = Article.objects.get(article_uid=article_uid)
            except Article.DoesNotExist:
                logger.critical(f"No article exists with '{article_uid}' article UID.")
                return None

            article.is_processing = False
            article.is_failed = True
            article.save()

        raise Exception(f"Bot was unable to enter 'started' state after {max_retries} attempts: {res.text}")


@shared_task
def celery_fetch_wp_published_articles_task(user_id):
    user = User.objects.get(id=user_id)
    website = user.current_active_website

    wp_integration = WordpressIntegration.objects.filter(website=website).first()
    
    credentials = google_integration_utils.get_google_oauth2_credentials(user, "google-search-console")
    
    if not credentials:
        sites = None
    else:
        sites = google_integration_utils.get_site_name_on_gsc(credentials, website.domain)

    try:
        posts = fetch_published_wp_posts(wp_integration, 40)

        existing_post_ids = set(
            WordpressPublishedArticle.objects.filter(website=website)
            .values_list("post_id", flat=True)
        )
        new_posts = [p for p in posts if p.get("id") not in existing_post_ids]

        start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')

        def process_post(post):
            try:
                article = create_article_from_wp_post(post, website)

                if not article:
                    return {"post_id": post.get("id"), "status": "error", "error": "Failed to create article"}

                with ThreadPoolExecutor(max_workers=2) as executor:
                    future_img = executor.submit(fetch_and_create_featured_image, post, wp_integration)
                    
                    if credentials:
                        future_gsc = executor.submit(
                            google_integration_utils.fetch_gsc_position_for_url,
                            credentials, sites, post.get("link"), start_date, end_date
                        )
                    else:
                        future_gsc = None

                    featured_image = future_img.result()
                    gsc_position = future_gsc.result() if future_gsc else None

                save_wordpress_published_article(article, post, featured_image, website, gsc_position)
                return {"post_id": post["id"], "status": "success"}

            except Exception as e:
                logger.critical(f"Error processing post {post.get('id')}: {e}", exc_info=True)
                return {"post_id": post.get("id"), "status": "error", "error": str(e)}

        with ThreadPoolExecutor(max_workers=10) as executor:
            results = list(executor.map(process_post, new_posts))

        return {"status": "completed", "results": results}

    except Exception as e:
        logger.critical(f"Exception during processing: {e}", exc_info=True)
        return {"status": "error", "message": str(e)}

@shared_task
def celery_pseo_title_gen(user_id, no_of_titles, pattern, example_text, keyword_md5_hash, seo_title_id):
    try:
        user = User.objects.get(id=user_id)
        website = user.current_active_website
        keyword = Keyword.objects.get(keyword_md5_hash=keyword_md5_hash)
        seo_title = ProgrammaticSeoTitle.objects.get(id=seo_title_id) 
        if not seo_title:
            return {'status': 'failed', 'message': 'Programmatic SEO title record not found.'}
        
        seo_titles = get_programmatic_seo_using_llm(no_of_titles, pattern, example_text)        

        try:
            article_bulk_create = []
            for i, title in enumerate(seo_titles.titles):
                # Ensure title is a string
                if i > no_of_titles-1:
                    break
                if not isinstance(title, str):
                    logger.warning(f"Skipping non-string title at index {i}: {title}")
                    continue
                
                cleaned_title = unescape_amp_char(title)
                        
                article_bulk_create.append(Article(
                    article_uid=generate_article_uid(user.username),
                    website=website,
                    title=cleaned_title,
                    keyword=keyword,
                    article_status="draft",                    
                ))                         
            created_articles = Article.objects.bulk_create(article_bulk_create)            
        except Exception as err: 
            seo_title.is_processing= False
            seo_title.save()          
            logger.critical(err)
            return {"status": "error","reason": "article_creation_failed","message": "Failed to create articles."}

        try:
            seo_title.articles.add(*created_articles)
            seo_title.is_processing= False
            seo_title.save()            
        except Exception as err:
            seo_title.is_processing= False
            seo_title.save()            
            logger.critical(err)
            return {"status": "error", "reason": "seo_title_creation_failed", "message": "Failed to create SEO title."}

        user.titles_generated += no_of_titles if len(seo_titles.titles) > no_of_titles else len(seo_titles.titles)
        user.total_titles_generated += no_of_titles if len(seo_titles.titles) > no_of_titles else len(seo_titles.titles)
        user.save()            
        
        return {"status": "success", "message": f"Successfully generated {len(seo_titles.titles)} SEO titles."}

    except Exception as err:        
        seo_title.is_processing= False
        seo_title.save() 
        logger.critical(err)
        return {"status": "error", "reason": "unexpected_error", "message": "An unexpected error occurred. Please try again."}


@shared_task
def celery_generate_glossary_words(user_id, project_id, word, no_of_words):
    '''
    task for generate glossary words for topics
    '''
    user = User.objects.get(id=user_id)
    website = user.current_active_website
    glossary_words = get_glossary_words_using_llm(word, no_of_words)

    if not glossary_words :
        return {
                "status": "rejected",
                "reason": "no_words_generated",
                "message": "No glossary words could be generated. Try different input.",
            }
    
    glossary_topic = GlossaryTopic.objects.get(website=website,project_id=project_id)    

    with transaction.atomic():
        glossary_topic.glossary_words = [unescape_amp_char(glossary_word.strip()) for glossary_word in glossary_words.titles]
        glossary_topic.save()

    user.glossary_topic_generated += 1
    user.save()
    
    return {'status': 'success', 'message': 'Successfully generated the glossary words.'}



@shared_task
def celery_get_competitors_keywords_from_domain(user_email:str, competitor_domain: str, selected_location: dict, max_keywords_allowed:int):
    user = User.objects.get(email = user_email)
    website = user.current_active_website
    # Fetch keywords.
    try:
        comp = website.competitor_set.get(domain=competitor_domain)
        all_keyword_data: List[Dict] = get_keywords_from_domain(competitor_domain)['data'][:2000]
    except Exception as err:
        logger.critical(f"{err}")
        comp.keywords_generated = True
        comp.in_processing = False
        comp.save()
        return

    # Remove block keywords
    block_keywords = BlockKeywords.objects.all().values_list('keyword', flat=True)
    all_keyword_data_copy = copy.copy(all_keyword_data)

    for keyword_data in all_keyword_data:
        if any(block_kw in keyword_data['keyword'] for block_kw in block_keywords):
            all_keyword_data_copy.remove(keyword_data)

    all_keyword_data = all_keyword_data_copy
    competitor_keywords: Dict = {
        f"{kw_data['keyword']}": kw_data['position'] for kw_data in all_keyword_data
    }

    # Separate into existing and new keyword objects.
    existing_keywords: List[str] = list(
        Keyword.objects.filter(
            keyword__in=[kw for kw in list(competitor_keywords.keys())]
        ).values_list('keyword', flat=True)
    )
    new_keywords: List[str] = list(filter(lambda kw: kw not in existing_keywords, list(competitor_keywords.keys())))

    chunk_size = 100
    keyword_chunks = list(chunker(new_keywords, chunk_size))

    with ThreadPoolExecutor(max_workers=max(1, min(len(keyword_chunks), 32))) as executor:
        results = executor.map(lambda chunk: get_keyword_volume_data(chunk, country="global"), keyword_chunks)
    new_keywords_volume_data = []
    for result in results:
        new_keywords_volume_data.extend(result)

    # # Add existing keywords to competitor.
    # add_existing_keywords(comp, existing_keywords, "global", "keywordseverywhere")

    comp.keywords_generated = True
    comp.save()

    logger.info(f"Keywords generated for {competitor_domain}")

    new_keywords_with_data = []
    for keyword_data in new_keywords_volume_data:
        cleaned_keyword: str = re.sub(r'[^a-zA-Z0-9\s]', "", keyword_data['keyword'])

        # if there is no data for the keyword, add it with default values
        if not keyword_data["vol"]:
            keyword_obj = create_keyword_object(user, cleaned_keyword,
                                                with_default_values=True,
                                                country=selected_location['country_iso_code'],
                                                source="user-keyword")
            new_keywords_with_data.append(keyword_obj)
            continue

        keyword_obj = create_keyword_object(user, cleaned_keyword, **{
            "source": "keywordseverywhere",
            "country": selected_location['country_iso_code'],
            "serp_position": None,
            "volume": keyword_data["vol"],
            "cpc_currency": keyword_data['cpc']['currency'],
            "cpc_value": keyword_data['cpc']['value'],
            "paid_difficulty": keyword_data["competition"],
            "trend": keyword_data["trend"]
        })
        new_keywords_with_data.append(keyword_obj)

    try:
        if (user.keywords_generated + len(new_keywords_with_data)) > max_keywords_allowed:
            new_keywords_with_data = new_keywords_with_data[:max_keywords_allowed - user.keywords_generated]

        project_name = f"Competitor-Keywords-{comp.domain}"
        # Create a new KeywordProject instance
        keyword_project = KeywordProject.objects.create(
            website=website,
            project_name=project_name,
            project_id=str(uuid.uuid4())[:16],
            location_iso_code=selected_location['country_iso_code'].lower(),
        )

        # save all keywords to the database
        with transaction.atomic(using='default'):
            # Skip keyword if it already exists in the database
            Keyword.objects.bulk_create(new_keywords_with_data , ignore_conflicts=True)
            user.keywords_generated += len(new_keywords_with_data)
            user.total_keywords_generated += len(new_keywords_with_data)
            user.save()

        # Add all new keywords to the keyword project
        with transaction.atomic(using='default'):
            keyword_project.keywords.add(*new_keywords_with_data)

        # update the total traffic volume
        keyword_project.total_traffic_volume = sum([kw.volume for kw in new_keywords_with_data])
        keyword_project.save()
        comp.associated_keyword_project = keyword_project
        comp.in_processing = False
        comp.save()
    except Exception as err:
        logger.critical(err)
        comp.in_processing = False
        comp.save()
        return
    
@shared_task
def celery_fetch_gsc_position_for_wp(user_id):
    '''
    Background celery task to fetch the gsc position for fetched Wp Published articles.
    '''
    
    user = User.objects.get(id=user_id)
    website = user.current_active_website

    wp_articles = WordpressPublishedArticle.objects.select_related("article").filter(website=website)

    if not wp_articles.exists():
        return {"error": "failed", "message": "WordPress published articles not found."}

    credentials = google_integration_utils.get_google_oauth2_credentials(user, "google-search-console")

    if not credentials:
        return {"status": "skipped", "message": "GSC credentials not found. Skipping GSC position update."}

    sites = google_integration_utils.get_site_name_on_gsc(credentials, website.domain)
    start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
    end_date = datetime.datetime.now().strftime('%Y-%m-%d')

    results = []
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_article = {}

        for wp_article in wp_articles:
            article_link = getattr(wp_article.article, "article_link", None)
            if article_link:
                future = executor.submit(
                    google_integration_utils.fetch_gsc_position_for_url,
                    credentials, sites, article_link, start_date, end_date
                )
                future_to_article[future] = wp_article

        for future in future_to_article:
            wp_article = future_to_article[future]
            try:
                gsc_position = future.result()
                wp_article.gsc_position = round(gsc_position, 2) if gsc_position is not None else None
                wp_article.save()
                results.append({"post_id": wp_article.post_id, "gsc_position": gsc_position})
            except Exception as e:
                results.append({"post_id": wp_article.post_id, "error": str(e)})

    return {
        "status": "completed",
        "updated_articles": results
    }
    
@shared_task
def celery_generate_keyword_from_icp(user_id, num_keywords, icp, keyword_project_id, selected_location):
    '''
    Celery task to generate Keywords with icp
    '''
    user = User.objects.get(id=user_id)
    website = user.current_active_website
    
    keyword_project = KeywordProject.objects.get(project_id=keyword_project_id)
    keywords = get_keywords_using_llm(icp, num_keywords)

    # Check block keywords list
    block_keywords = BlockKeywords.objects.all().values_list('keyword', flat=True)
    for keyword in keywords:
        if any(block_kw in keyword for block_kw in block_keywords):
            return {
                "success": False,
                "reason": "blocked_keyword_used",
                "message": f"You cannot add '{keyword}' keyword. If you feel this is incorrect, please contact us via live chat."
            }
    
    new_keywords = []
    for keyword in keywords:
        cleaned_keyword: str = re.sub(r'[^\w\s]', "", keyword)

        # if there is no data for the keyword, add it with default values
        keyword_obj = create_keyword_object(user, cleaned_keyword,
                                            with_default_values=True,
                                            country=selected_location['country_iso_code'],
                                            source="user-keyword")
        new_keywords.append(keyword_obj)

    if new_keywords.__len__() == 0:
        return {
            "success": False,
            "reason": "no_keywords_found",
            "message": "No keywords found. Please try with different keywords."
        }

    try:                        
        # save all keywords to the database
        with transaction.atomic(using='default'):
            # Skip keyword if it already exists in the database
            Keyword.objects.bulk_create(new_keywords , ignore_conflicts=True)

        # Add all new keywords to the keyword project
        with transaction.atomic(using='default'):
            keyword_project.keywords.add(*new_keywords)

        # update the total traffic volume
        keyword_project.total_traffic_volume = 0
        keyword_project.save()
        
    except Exception as e:
        logger.critical(f"Error occured in ICP to Kw {e}")