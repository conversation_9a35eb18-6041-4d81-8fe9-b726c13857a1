import os
import re
import json
import logging
import datetime
from typing import List, Dict
from zoneinfo import ZoneInfo

import requests
from django.conf import settings
from django.http import JsonResponse
from django.urls import reverse
from rest_framework.request import Request
from rest_framework.decorators import api_view
from langchain_core import exceptions

from AbunDRFBackend.settings import (ABUN_NOTIFICATION_EMAIL, ADIL_EMAIL, AMIN_EMAIL, DEBUG, REDIS_TASK_DATA_DB, REDIS_ART_GEN_EXPIRY, FLY_API_HOST,
                                     FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN, FLY_ARTICLE_INTERNAL_LINK_APP_NAME, FLY_ARTICLE_INTERNAL_LINK_IMAGE_URL,
                                     K8_JOB_RETRIES)
from mainapp.decorators import cors_allow_all
from mainapp.email_messages import (article_generation_success_email_body, article_generation_failed_email_body,
                                    article_generation_failed_email_body_for_admins)
from mainapp.json_responses import JsonResponse404
from mainapp.models import (KubernetesJob, Article, User, AllArticlesStats, AIArticleImage, AutomationProject,
                            Website, ArticleInternalLinkQueue, AICalculator)
from mainapp.utils import (publish_article_to_wix, send_email, save_featured_image, get_unsplash_images, get_word_count, sanitize_url_slug,
                           generate_AI_feature_image__sync, publish_article_to_wp, publish_article_to_wf, rephrase_article_title, get_table_of_content,format_toc_markdown, add_heading_ids, get_redis_connection, generate_k8_job_id, create_k8_job, add_calculator_branding, remove_calculator_branding)
from mainapp.stripe_utils import get_stripe_product_data
from mainapp.words_translations import get_faq_translation, get_credits_translation, get_Unsplash_by_translation
from mainapp.custom_featured_images import generate_custom_feature_image
from mainapp.website_scanning import WebsiteScanning
from mainapp.competitor_finder import CompetitorFinder
from mainapp.tasks import celery_check_flyio_provisioning

logger = logging.getLogger('abun.webhooks')


# @cors_allow_all
# @api_view(['POST'])
# def content_plan_webhook(request: Request) -> JsonResponse:
#     """
#     Webhook endpoint that handles all requests from Content Plan generation kubernetes job.

#     :param request: Django Rest Framework's Request object
#     """
#     event_type: str = request.data['event']
#     job_id = request.data['job_id']

#     # ===============================================================================
#     # ---------------------- CONTENT PLAN GENERATION - SUCCESS ----------------------
#     # ===============================================================================
#     if event_type == 'content_plan_generation_completed':
#         start_time = time.time()
#         logger.debug("Got `content_plan_generation_completed` event type")

#         connected_website_data: Dict = request.data['data']['connected_website']
#         competitor_domains: List[str] = request.data['data']['competitor_domains']
#         logger.debug(competitor_domains)

#         connected_website_domain_data: Dict = connected_website_data['domain_data']
#         connected_website_keywords: Dict = connected_website_data['keywords']
#         connected_website_user_keywords: Dict = connected_website_data['user_keywords']

#         try:
#             task = KubernetesJob.objects.get(job_id=job_id)
#             connected_website: Website = task.user.current_active_website
#         except KubernetesJob.DoesNotExist:
#             return JsonResponse(status=500, data={'message': "Could not find associated KubernetesJob. "
#                                                              "Website or task might have been deleted"})
#         connected_website.content_plan_task_progress = 75

#         # Add all domain data
#         connected_website.organic_traffic = connected_website_domain_data['organic_traffic']
#         connected_website.organic_keywords = connected_website_domain_data['organic_keywords']
#         connected_website.domain_authority = connected_website_domain_data['domain_authority']
#         connected_website.total_backlinks = connected_website_domain_data['total_backlinks']
#         connected_website.follow_count = connected_website_domain_data['follow']
#         connected_website.no_follow_count = connected_website_domain_data['no_follow']
#         connected_website.referring_domains_count = connected_website_domain_data['referring_domains']
#         connected_website.save()

#         # create keyword object for all user added website keywords
#         if connected_website_user_keywords['new']:
#             logger.debug(
#                 f"[*] content plan {connected_website.domain} - Creating and adding new user provided keywords"
#             )
#             added_user_keywords = add_keywords(connected_website,
#                                                connected_website_user_keywords['new'],
#                                                user_added_keywords=True)
#             connected_website.selected_keywords.add(*added_user_keywords)

#         if connected_website_user_keywords['existing']:
#             logger.debug(
#                 f"[*] content plan {connected_website.domain} - Creating and adding existing user provided keywords"
#             )
#             added_user_keywords = add_existing_keywords(connected_website,
#                                                         [kw['keyword'] for kw in
#                                                          connected_website_user_keywords['existing']],
#                                                         country=connected_website_user_keywords['existing'][0]['country'],
#                                                         source=connected_website_user_keywords['existing'][0]['source'],
#                                                         user_added_keywords=True)
#             connected_website.selected_keywords.add(*added_user_keywords)

#         connected_website.content_plan_task_progress = 80
#         connected_website.save()
#         # create keyword object for all api website keywords
#         if connected_website_keywords['new']:
#             logger.debug(
#                 f"[*] content plan {connected_website.domain} - Creating and adding new api provided keywords"
#             )
#             add_keywords(connected_website, connected_website_keywords['new'])

#         if connected_website_keywords['existing']:
#             logger.debug(
#                 f"[*] content plan {connected_website.domain} - Creating and adding existing api provided keywords"
#             )
#             add_existing_keywords(connected_website,
#                                   [kw['keyword'] for kw in connected_website_keywords['existing']],
#                                   country=connected_website_keywords['existing'][0]['country'],
#                                   source=connected_website_keywords['existing'][0]['source'])

#         connected_website.content_plan_task_progress = 85
#         connected_website.save()
#         # --------------------- Create and add competitors ---------------------
#         competitors_objects = []

#         for domain in competitor_domains:
#             competitors_objects.append(
#                 Competitor(
#                     website=connected_website,
#                     domain=domain,
#                     protocol="https",
#                     logo_url=f"https://{os.environ['CLOUDFLARE_R2_DOMAIN']}/{os.environ['CLOUDFLARE_R2_ENV']}/logo/{domain}"
#                 )
#             )

#         Competitor.objects.bulk_create(competitors_objects)
#         connected_website.content_plan_task_progress = 90
#         connected_website.save()

#         # TODO: performance issues somewhere till this line

#         # --------------------- Generate Article Titles ---------------------
#         logger.debug(f"[*] content plan {connected_website.domain} - Generating titles")
#         art = ArticleTitleGenerator(connected_website)
#         art.generate_titles(15)
#         connected_website.content_plan_task_progress = 93
#         connected_website.save()
#         art.generate_titles(15, "how_to")
#         connected_website.user.titles_generated += 30
#         connected_website.user.total_titles_generated += 30
#         connected_website.user.save()

#         connected_website.content_plan_task_progress = 95
#         connected_website.save()

#         # --------------------- Get top 30 keywords for content plan page ---------------------
#         top_keywords = (
#                                Keyword.objects.filter(website=connected_website) |
#                                Keyword.objects.filter(competitor__in=connected_website.competitors.all())
#                        ).order_by('-volume').distinct()[:30]

#         connected_website.top_keywords_by_volume.add(*top_keywords)

#         # Get performance chart
#         performance_chart_url = "https://images.bannerbear.com/direct/vXJYw31DVQKZo0gD6P/requests" \
#             "/000/048/667/943/VJqEKwxkyzGm0qleQNP8dj4vL/828338605474b933f4be6208fbfd55d6d13734ab.png"
#         connected_website.content_plan_performance_chart_url = performance_chart_url
#         # performance_chart = generate_bannerbear_performance_chart__sync(logo_url=connected_website.logo_url,
#         #                                                                 website_domain=connected_website.domain)

#         # if performance_chart:
#         #     connected_website.content_plan_performance_chart_url = performance_chart['image_url_png']

#         # mark this task as completed and send back 200 response to k8 job
#         connected_website.content_plan_generation_status = 'done'
#         task.status = 'completed'
#         connected_website.content_plan_task_progress = 100
#         connected_website.save()
#         task.save()

#         # log the event
#         add_website_log(
#             user=connected_website.user,
#             domain=connected_website.domain,
#             message=f"Content plan for {connected_website.domain} generated successfully.",
#             connection_type='connected'
#         )

#         logger.info(
#             f"[*] `content_plan_generation_completed` webhook took {time.time() - start_time} seconds to complete.")

#         # send email to user
#         if connected_website.user.send_notification_emails:
#             email_message: str = content_plan_success_email_body(connected_website.user.username)
#             subject: str = "Your Content Plan is Ready"
#             send_email(connected_website.user.email, ABUN_NOTIFICATION_EMAIL, "Team Abun", subject, email_message)

#         return JsonResponse(status=200, data={'message': 'OK'})

#     # ==============================================================================
#     # ---------------------- CONTENT PLAN GENERATION - FAILED ----------------------
#     # ==============================================================================
#     elif event_type == 'content_plan_generation_failed':
#         logger.debug("Content Plan generation failed :( Increment fail count by 1")
#         job_id: str = request.data['job_id']
#         try:
#             task = KubernetesJob.objects.get(job_id=job_id)
#         except KubernetesJob.DoesNotExist:
#             return JsonResponse(status=500, data={'message': "Could not find associated KubernetesJob. "
#                                                              "Website or task might have been deleted"})
#         task.retry_attempts += 1

#         if task.retry_attempts > settings.K8_JOB_RETRIES:
#             task.status = 'failed'
#             task.fail_reason = "Max retry attempts exceeded"
#             website: Website = task.website
#             website.content_plan_generation_status = 'failed'
#             website.content_plan_task_progress = 0
#             website.save()

#             # send email
#             if website.user.send_notification_emails:
#                 email_body: str = content_plan_failed_email_body(website.user.username)
#                 send_email(website.user.email,
#                            ABUN_NOTIFICATION_EMAIL,
#                            "Team Abun",
#                            "Retry content plan generation with 1-click",
#                            email_body)

#         task.save()

#         return JsonResponse(status=200, data={'message': 'OK'})

#     else:
#         logger.exception(f"Kubernetes Job sent unrecognized 'event_type' {event_type} to content plan webhook")
#         return JsonResponse(status=501, data={'message': f"Unrecognized event type {event_type}"})


@cors_allow_all
@api_view(['POST'])
def article_generation_webhook(request: Request) -> JsonResponse:
    """
    Webhook for `article generation` kubernetes job.

    :param request: Django Rest Framework's Request object
    """
    event_type: str = request.data['event']
    job_id: str = request.data['job_id']

    logger.debug(event_type)
    logger.debug(job_id)

    try:
        task = KubernetesJob.objects.get(job_id=job_id)
        user: User = task.user
    except KubernetesJob.DoesNotExist:
        logger.error(f"Kubernetes Job {job_id} does not exists.")
        return JsonResponse(status=500, data={'message': "Could not find associated KubernetesJob. "
                                                         "Website or task might have been deleted"})

    # ==========================================================================
    # ---------------------- ARTICLE GENERATION - SUCCESS ----------------------
    # ==========================================================================
    if event_type == 'article_generation_v2_completed':
        v2: bool = event_type == 'article_generation_v2_completed'
        article_data: Dict = request.data['data']['article_data']

        if v2:
            logger.debug("Article Generation V2")

        article_uid: str = request.data['data']['article_uid']
        task.article_generation_cost_info = request.data['data']['cost_info']
        task.save()

        article: Article = Article.objects.get(article_uid=article_uid)

        article.summary_inputs = article_data.get('summary_inputs', '').replace("\x00", "\uFFFD")
        article.merged_summary = article_data.get('merged_summary', '').replace("\x00", "\uFFFD")
        article.article_outlines = article_data.get('outline_generation', [])
        article.crewai_output = article_data.get('crewai_verbose', '').replace("\x00", "\uFFFD")
        transcript_raw = article_data.get('transcript') or ''
        article.transcript = transcript_raw.replace("\x00", "\uFFFD")
        article.url_slug = sanitize_url_slug(article.title.lower().replace(" ","-")[:50])

        # -------------------- Assemble Blog Post Content --------------------
        # Blog Title (h1 tag)
        # Intro (para)
        # Sub-Heading 1 (h2)
        # Sub-Heading 1 Image
        # Sub-Heading 1 content
        # Sub-Heading 2 (h2)
        # Sub-Heading 2 Image
        # Sub-Heading 2 content
        # .
        # .
        # .
        # F.A.Q
        # TL;DR

        # ExternalLinking - convert first matching phrase throughout the entire body content with link.
        # We need to do this before adding images otherwise it might replace text in the <img> tag.
        # external_backlinks_preference: "no-follow" | "off" | "follow"
        if user.external_backlinks_preference != "off":
            total_external_links: int = 0
            all_externalLinks: List[Dict] = article_data["external_links"]
            external_linked_phrases = set()

            # Iterate through each section in the body content
            for section in article_data["body_content"]:
                # Check if there are remaining interlinking phrases
                if all_externalLinks:
                    # Iterate through each interlinking phrase that has not been linked yet
                    for item in [i for i in all_externalLinks if i["phrase"] not in external_linked_phrases]:
                        # Skip the section if it already contains a hyperlink
                        if any("<a href=" in paragraph for paragraph in section["paragraphs"]):
                            continue
                        # Iterate through each paragraph in the section
                        for i, paragraph in enumerate(section["paragraphs"]):
                            # Skip the paragraph if an interlink has already been added to the paragraph
                            if "<a href=" in paragraph:
                                break
                            # Check if the interlinking phrase is present in the paragraph
                            if re.search(re.escape(item["phrase"]), paragraph, re.IGNORECASE):
                                # Create the hyperlink with the interlinking phrase and its corresponding link
                                pattern = r'\b' + re.escape(item["phrase"]) + r'\b'
                                match = re.search(pattern, paragraph, re.IGNORECASE)
                                if match:
                                    link = f'<a href="{item["link"]}" rel="{user.external_backlinks_preference.replace("-", "")}">{match.group()}</a>'
                                else:
                                    # Create the hyperlink with the interlinking phrase and its corresponding link
                                    link = f'<a href="{item["link"]}" rel="{user.external_backlinks_preference.replace("-", "")}">{re.search(re.escape(item["phrase"]), paragraph, re.IGNORECASE).group()}</a>'
                                # Replace the first occurrence of the interlinking phrase with the hyperlink in the paragraph
                                section["paragraphs"][i] = re.sub(r'\b' + re.escape(item["phrase"]) + r'\b', link, paragraph, count=1, flags=re.IGNORECASE)
                                # Update the count of total internal links added
                                total_external_links += 1
                                # Add the linked phrase to the set of linked phrases
                                external_linked_phrases.add(item["phrase"])
                                break

            article.external_link_count = total_external_links

        # Assemble the main article body
        assembled_body: str = ""
        for index, section in enumerate(article_data['body_content']):  # [{heading: str, paragraphs: List[str]}, ...]
            # Assemble all section paragraphs and mark important sentences/phrases as bold.
            assembled_paragraphs: str = "\n\n".join(section['paragraphs'])
            for text in section['bold']:
                match = re.search(text, assembled_paragraphs)
                if match:
                    match_text: str = str(match.group())
                    # Avoid bolding text within an anchor tag
                    if f'<a href="{match_text}"' not in assembled_paragraphs:
                        assembled_paragraphs = re.sub(
                            text, f"**{match_text}**", assembled_paragraphs, count=1, flags=re.IGNORECASE
                        )

            # Add section heading and paragraphs to assembled body
            assembled_body += f"## **{section['heading']}**\n\n{assembled_paragraphs}\n\n"
            table_section = article_data.get("table_section") or {}
            bullet_points = article_data.get("bullet_points") or {}
            
            # Check if the current section heading matches the table section
            if (isinstance(table_section, dict) and section["heading"] in table_section.get("section_heading", [])):
                # Convert TableData to HTML table
                html = "<table border='1'>\n"
                html += "  <tr>\n"

                # Add table headers
                for column in article_data["table_section"]["columns"]:
                    html += f"    <th style='text-align: center;'>{column}</th>\n"
                html += "  </tr>\n"

                # Add table rows
                for row in article_data["table_section"]["rows"]:
                    html += "  <tr>\n"
                    for cell in row:
                        html += f"    <td style='text-align: center;'>{cell}</td>\n"
                    html += "  </tr>\n"

                html += "</table>"
                assembled_body += f"{html}\n\n"

            # Check if the current section heading matches the bullet points section
            elif ( isinstance(bullet_points, dict) and section["heading"] in bullet_points.get("section_heading", [])):
                assembled_body += f"{article_data['bullet_points']['bulleted_list_markdown']}\n\n"

        # Ensure no extra newlines at the end of the article body
        assembled_body = assembled_body.strip()

        # Add images between sub heading and subheading content
        total_image_count: int = 0
        images_appended: List[str] = []
        ai_image_objects = []
        for image in article_data['image_suggestions']:

            if 'image_source' not in image:
                continue

            if image['image_source'] == 'no_image':
                continue

            elif image['image_source'] == 'google':
                # image_credits: str = image['image_credits_name']
                image_credits_domain: str = image['image_credits_domain']

                # image_credits_link: str = image['image_credits_url']
                subheading: str = image['main']

                if subheading in assembled_body and image['image_url'] not in images_appended:
                    assembled_body = str(re.sub(
                        re.escape(f"{subheading}**"),
                        f"{subheading}**\n\n<img src=\"{image['image_url']}\" alt=\"{image['search']}\" style=\"width: 50%; height: auto; "
                        f"margin: 0 auto; display: block; min-width: 250px;\" />"
                        f"<sub>"
                        f"{get_credits_translation(user.article_language_preference)}: <span>{image_credits_domain}</span>"
                        f"</sub>",
                        assembled_body,
                        count=1
                    ))
                    total_image_count += 1
                    images_appended.append(image['image_url'])

            elif image['image_source'] == 'unsplash':
                unsplash_link = "https://unsplash.com/?utm_source=Abun&utm_medium=referral"
                artist_link: str = f"{image['image_artist_link']}?utm_source=Abun&utm_medium=referral"
                download_location: str = image['download_location']
                subheading: str = image['main']

                if subheading in assembled_body and image['image_url'] not in images_appended:
                    unsplash_details = {
                        "artist_name": image['image_artist_name'],
                        "artist_link": artist_link,
                        "unsplash_link": unsplash_link
                    }
                    assembled_body = str(
                        re.sub(
                            re.escape(f"{subheading}**"),
                            f"{subheading}**\n\n<img src=\"{image['image_url']}\" alt=\"{image['search']}\" style=\"width: 50%; height: auto; margin: 0 auto; display: block;\" />"
                            f"<br/>"
                            f"<sub>"
                            f"""{
                                get_Unsplash_by_translation(
                                    user.article_language_preference,
                                    unsplash_details
                                )
                            }"""
                            f"</sub>",
                            assembled_body,
                            count=1
                        )
                    )

                    # trigger unsplash download count api
                    try:
                        requests.get(f"{download_location}&client_id=9q8bIZG_oMuLNlObcNiuzWaqmzY1ShSNX72_KDpGzxI")
                    except Exception as err:
                        logger.error(f"Could not trigger unsplash download counter for {download_location}: {err}")

                    total_image_count += 1
                    images_appended.append(image['image_url'])

            elif image['image_source'] == 'ai_image_generation':
                subheading: str = image['main']

                if subheading in assembled_body and image['image_url'] not in images_appended:
                    assembled_body = str(
                        re.sub(
                            re.escape(f"{subheading}**"),
                            f"{subheading}**\n\n<img src=\"{image['image_url']}\" alt=\"{image['search']}\" style=\"width: 50%; height: auto; margin: 0 auto; margin-bottom: 10px; display: block;\" />",
                            assembled_body,
                            count=1
                        )
                    )

                    total_image_count += 1
                    images_appended.append(image['image_url'])

                    ai_image_objects.append(
                        AIArticleImage(
                            image_url=image['image_url'],
                            context_title=image['main'],
                            generated_context_description=image['image_context_description'],
                        )
                    )

            else:
                raise Exception(f"Bad image_source value \"{image['image_source']}\"")
            
        if ai_image_objects:
            AIArticleImage.objects.bulk_create(ai_image_objects, batch_size=100)
            
        article.image_count = total_image_count

        # Assemble the F.A.Q section
        assembled_faq = "## " + get_faq_translation(user.article_language_preference)
        for index, item in enumerate(article_data.get('faq') or []):
            assembled_faq += f"\n\n#### {index + 1}. {item['question']}\n{item['answer']}"


        article_markdown: str = f"""
{assembled_body}

{ assembled_faq if not DEBUG or user.toggle_faq else ""}

{article_data.get('tldr') or ""}"""

        article_markdown = re.sub(r'\s*—\s*', ', ', article_markdown)
        article_intro = re.sub(r'\s*—\s*', ', ', article_data['intro_para'])

        try:
            if not DEBUG or user.toggle_toc:
                tabel_of_content = get_table_of_content(user.article_language_preference, article_markdown)
            else:
                tabel_of_content = None
        except exceptions.OutputParserException:
            logger.error("Failed to parse table of content")
            tabel_of_content = None

        except Exception as err:
            logger.critical(f"Failed to generate table of content: {err}")
            tabel_of_content = None

        if not tabel_of_content:
            complete_article_markdown = f""" {article_intro} {article_markdown}"""
        else:
            toc_markdown = format_toc_markdown(tabel_of_content)
            updated_markdown_content = add_heading_ids(article_markdown)
            complete_article_markdown: str = f"""{article_intro}
            
{toc_markdown}

{updated_markdown_content}"""

        article.article_description = (article_data.get("article_description") or '').replace("\x00", "\uFFFD")
        article.word_count = get_word_count(complete_article_markdown)
        article.content = complete_article_markdown.replace("\x00", "\uFFFD")
        article.save()

        # Generate and save featured image.
        feature_image = article.selected_featured_image
        selected_template = user.feature_image_template_id

        if feature_image and feature_image.template_image_url:
            template_photo = feature_image.template_image_url

        else:
            template_photos = get_unsplash_images(article.title)

            if not template_photos:
                template_photo = "https://res.cloudinary.com/diaiivikl/image/upload/v1690186987/featured_image_1200x628.png"
            else:
                template_photo = template_photos[0]['url']

        try:
            if not user.feature_image_required:
                raise Exception("Feature image generation is turned off for this website")

            else:
                if "-text" in user.feature_image_template_id:
                    template_photo_url = "https://res.cloudinary.com/diaiivikl/image/upload/v1709385716/ai-generated-image_rv1ice.jpg"
                    ai_image_response: Dict = generate_AI_feature_image__sync(article_uid,
                                                                              user.feature_image_template_label == "premium" and "segmind" or "deepinfra")
                    save_featured_image(ai_image_response["image_url"], "ai_image_generation",
                                        article, user.feature_image_template_id, template_photo_url)

                else:
                    # Prompt gpt to make the title shorter
                    article_title: str = rephrase_article_title(article)

                    own_image_response: Dict = generate_custom_feature_image(
                        template_photo,
                        article.user.current_active_website.logo_url if article.user.show_logo_on_featured_image else "",
                        article_title,
                        article_uid,
                        selected_template,
                        True,
                        article.user.images_file_format,
                    )

                    if own_image_response["image_url"]:
                        save_featured_image(
                            own_image_response["image_url"],
                            "bannerbear",
                            article,
                            selected_template,
                            template_photo,
                        )

                    else:
                        raise Exception("Featured image generation failed")

        except Exception as err:
            # Use default image
            logger.error(f"{err}")
            logger.info(f"Using default featured image for {article_uid}")

            default_image_url: str = "https://res.cloudinary.com/diaiivikl/image/upload/v1690186987/" \
                                     "featured_image_1200x628.png"
            save_featured_image(default_image_url, 'defaultimage', article,
                                user.feature_image_template_id, default_image_url)

        # Save article content but keep it in processing state for internal linking
        article.save()

        # Get the internal linking keywords
        interlinking_original = article_data['interlinking_original']

        try:
            internal_link_keywords = json.dumps(interlinking_original)
        except json.JSONDecodeError:
            internal_link_keywords = []

        if not DEBUG:
            # Create article internal link queue entry to process internal links
            ArticleInternalLinkQueue.objects.create(
                article=article,
                internal_link_keywords=internal_link_keywords,
                status='queued',
                k8_job=task
            )

        else:
            # On dev submit the article directly to k8s
            # On staging submit the article directly to flyio
            # Generate job ID for article internal linking
            article_internal_link_job_id = generate_k8_job_id('articleinternallink', username=user.username)

            # Prepare internal link data
            article_internal_link_data = {
                'domain_id': article.website.id,
                'article_uid': article.article_uid,
                'article_job_id': job_id,
                'internal_link_keywords': interlinking_original,
                'abun_webhook_url': reverse('wh-k8-article-internal-link'),
                'article_language_preference': user.article_language_preference,
                'max_internal_backlinks': user.max_internal_backlinks,
            }

            # Store data in Redis
            with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                redis_connection.set(article_internal_link_job_id, json.dumps(article_internal_link_data))
                redis_connection.expire(article_internal_link_job_id, REDIS_ART_GEN_EXPIRY)

            if os.environ['K8_IMAGE_TAG'] != 'staging':
                # Use Kubernetes for development
                create_k8_job(
                    article_internal_link_job_id,
                    'article_internal_link',
                    article_internal_link_job_id,
                    user.id,
                    [article_internal_link_job_id]
                )

            else:
                # Use Fly.io for staging
                # Create Fly.io machine to process article internal linking
                cmd = f"python3 article_internal_link_generation.py {article_internal_link_job_id}"
                cmd = cmd.split()
                worker_props = {
                    "config": {
                        "image": FLY_ARTICLE_INTERNAL_LINK_IMAGE_URL,
                        "auto_destroy": True,
                        "init": {
                            "cmd": cmd
                        },
                        "restart": {
                            "policy": "on-failure",
                            "max_retries": K8_JOB_RETRIES
                        },
                        "guest": {
                            "cpu_kind": "shared",
                            "cpus": 1,
                            "memory_mb": 1024
                        }
                    },
                }

                res = requests.post(
                    f"{FLY_API_HOST}/apps/{FLY_ARTICLE_INTERNAL_LINK_APP_NAME}/machines",
                    headers={
                        'Authorization': f"Bearer {FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN}",
                        'Content-Type': 'application/json'
                    },
                    json=worker_props
                )

                if res.status_code != 200:
                    logger.error(f"Failed to send article internal link task to Fly.io: {res.text}")
                    
                    # Mark the article as completed without internal links
                    article.is_processing = False
                    article.is_generated = True
                    article.generated_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
                    article.internal_link_count = 0
                    article.save()

                    # Update stats
                    AllArticlesStats.objects.update_or_create(
                        article_uid=article_uid,
                        defaults={
                            'generated_on': datetime.datetime.now(tz=ZoneInfo('UTC')),
                            'is_successful': True  # Article is still successful, just without internal links
                        }
                    )

                    return JsonResponse(status=200, data={'message': 'OK'})

                # Get machine ID and start provisioning check
                machine_id = res.json()['id']

                # Run the celery flyio provisioning task
                celery_check_flyio_provisioning.delay(machine_id,
                                                      article_internal_link_job_id,
                                                      FLY_ARTICLE_INTERNAL_LINK_APP_NAME,
                                                      FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN)

        # Note: Email notification will be sent after article internal linking is complete

        # Delete the redis key
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.delete(job_id)

        return JsonResponse(status=200, data={'message': "OK"})

    # =========================================================================
    # ---------------------- ARTICLE GENERATION - FAILED ----------------------
    # =========================================================================
    elif event_type == 'article_generation_failed':
        error_message: str = request.data['error_message']
        logger.error(f"ARTICLE GENERATION TASK FAILED: {error_message}")

        task.retry_attempts += 1

        if task.retry_attempts >= settings.K8_JOB_RETRIES:
            stage_data: Dict = request.data['stage_data']
            article_uid: str = task.metadata

            article = Article.objects.get(article_uid=article_uid)
            article.is_processing = False
            article.is_failed = True
            article.summary_inputs = stage_data.get('summary_inputs', '').replace("\x00", "\uFFFD")
            article.merged_summary = stage_data.get('merged_summary', '').replace("\x00", "\uFFFD")
            article.article_outlines = stage_data.get('outline_generation', [])
            article.save()

            AllArticlesStats.objects.update_or_create(
                article_uid=article_uid,
                defaults={
                    'generated_on': datetime.datetime.now(tz=ZoneInfo('UTC')),
                    'is_successful': False
                }
            )

            user.articles_generated -= 1
            user.total_articles_generated -= 1
            user.save()

            task.status = 'failed'
            task.fail_reason = f"{error_message}. All K8 retry attempts have been exhausted."

            # send email to user
            if user.send_notification_emails:
                email_message: str = article_generation_failed_email_body(user.username)
                subject: str = "Article Generation Failed - Please Try Again"
                send_email(user.email, ABUN_NOTIFICATION_EMAIL, "Team Abun", subject, email_message)

            if not DEBUG:
                # send email to admin
                failed_article_email_message_for_admins = article_generation_failed_email_body_for_admins(user, article, error_message)

                send_email(
                    [ADIL_EMAIL, AMIN_EMAIL],
                    ABUN_NOTIFICATION_EMAIL,
                    "Team Abun",
                    f"Article Generation Failed for {user.email}",
                    failed_article_email_message_for_admins,
                    reply_to=user.email
                )

            # Delete the redis key
            with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                redis_connection.delete(job_id)

        task.save()
        return JsonResponse(status=200, data={'message': 'OK'})

    else:
        logger.exception(f"Kubernetes Job sent unrecognized 'event_type' {event_type} to article generation webhook")
        return JsonResponse(status=501, data={'message': f"Unrecognized event type {event_type}"})


@cors_allow_all
@api_view(['POST'])
def website_scanning_webhook(request: Request) -> JsonResponse:
    """
    Webhook for `website scanning` kubernetes job.

    :param request: Django Rest Framework's Request object
    """
    job_id: str = request.data['job_id']

    try:
        KubernetesJob.objects.get(job_id=job_id)
    except KubernetesJob.DoesNotExist:
        logger.error(f"Kubernetes Job {job_id} does not exists.")
        return JsonResponse(status=500, data={'message': "Could not find associated KubernetesJob. "
                                                         "Website or task might have been deleted"})

    pages_data = request.data.get('pages_data')
    website_domain = request.data['domain']
    stats = request.data['stats']
    update_only_stats = request.data['update_only_stats']
    rescan = request.data['rescan']

    # Get the website
    try:
        website = Website.objects.get(domain=website_domain)
    except Website.DoesNotExist:
        logger.error(f"No website exists with '{website_domain}' domain")
        return JsonResponse404()

    websiteScanning = WebsiteScanning(website, stats=stats, k8_job_id=job_id)

    if rescan:
        websiteScanning.update_webpage(pages_data)
    elif not update_only_stats:
        websiteScanning.store_website_pages_data(pages_data)
    else:
        websiteScanning.update_or_store_website_crawling_stats()

    return JsonResponse(status=200, data={'message': "OK"})


@cors_allow_all
@api_view(['POST'])
def competitor_finder_webhook(request: Request) -> JsonResponse:
    """
    Webhook for `competitor finder` kubernetes job.

    :param request: Django Rest Framework's Request object
    """
    job_id: str = request.data['job_id']

    try:
        KubernetesJob.objects.get(job_id=job_id)
    except KubernetesJob.DoesNotExist:
        logger.error(f"Kubernetes Job {job_id} does not exists.")
        return JsonResponse(status=500, data={'message': "Could not find associated KubernetesJob. "
                                                         "Website or task might have been deleted"})
    domain = request.data.get('domain')
    limit = request.data['limit']
    competitors = request.data['competitors']
    regenerate_competitor = request.data['regenerate_competitor']
    try:
        website = Website.objects.get(domain=domain)
    except Website.DoesNotExist:
        logger.critical(f"No website exists with '{domain}' domain")
        return JsonResponse404()

    logger.error(f"competitors: {competitors}")

    competitor = CompetitorFinder(user=website.user,domain=domain,limit=limit,regenerate_competitor=regenerate_competitor,k8_job_id=job_id)
    competitor.store_competitors(competitors)
    logger.info(f"job_id {job_id}")


    return JsonResponse(status=200, data={'message': "OK"})


@cors_allow_all
@api_view(['POST'])
def article_internal_link_webhook(request: Request) -> JsonResponse:
    """
    Webhook for `article internal linking` kubernetes job.

    :param request: Django Rest Framework's Request object
    """
    event_type: str = request.data['event']
    job_id: str = request.data['job_id']

    logger.debug(event_type)
    logger.debug(job_id)

    try:
        task = KubernetesJob.objects.get(job_id=job_id)
        user: User = task.user
    except KubernetesJob.DoesNotExist:
        logger.error(f"Kubernetes Job {job_id} does not exists.")
        return JsonResponse(status=500, data={'message': "Could not find associated KubernetesJob. "
                                                         "Website or task might have been deleted"})

    # Get the article internal link queue item
    try:
        queue_item = ArticleInternalLinkQueue.objects.filter(
            k8_job=task,
            status='processing'
        ).order_by('created_at').first()
    except ArticleInternalLinkQueue.DoesNotExist:
        queue_item = None
        logger.error(f"Article Internal Link Queue item for job {job_id} does not exist.")
        return JsonResponse(status=500, data={'message': "Could not find associated ArticleInternalLinkQueue item."})

    # ==========================================================================
    # ---------------------- INTERNAL LINKING - SUCCESS ----------------------
    # ==========================================================================
    if event_type == 'internal_linking_completed':
        article_uid: str = request.data['data']['article_uid']
        internal_links_data: List[Dict] = request.data['data']['internal_links_data']

        # Get the article
        article: Article = Article.objects.get(article_uid=article_uid)

        # Process internal links and add them to article content and update internal link results
        total_internal_links: int = 0
        linked_phrases = set()
        article_content = article.content

        for item in internal_links_data['internal_links']:
            if item["phrase"] not in linked_phrases:
                # Search for the phrase in the content and replace with link
                pattern = r'\b' + re.escape(item["phrase"]) + r'\b'
                match = re.search(pattern, article_content, re.IGNORECASE)
                if match:
                    link = f'<a href="{item["link"]}">{match.group()}</a>'
                    article_content = re.sub(pattern, link, article_content, count=1, flags=re.IGNORECASE)
                    total_internal_links += 1
                    linked_phrases.add(item["phrase"])

        # Update article content and internal link count
        article.content = article_content
        article.internal_link_count = total_internal_links

        # Update internal link results
        try:
            article.internal_link_results = json.dumps(internal_links_data)
        except json.JSONDecodeError:
            article.internal_link_results = []

        # Update suggested internal links
        try:
            article.suggested_internal_links = json.dumps(internal_links_data['suggested_internal_links'])
        except json.JSONDecodeError:
            article.suggested_internal_links = []

        # Mark article as completed
        article.is_processing = False
        article.is_generated = True
        article.generated_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
        article.save()

        # Update stats
        AllArticlesStats.objects.update_or_create(
            article_uid=article_uid,
            defaults={
                'generated_on': datetime.datetime.now(tz=ZoneInfo('UTC')),
                'is_successful': True
            }
        )

        if queue_item:
            # Mark queue item as completed
            queue_item.status = 'completed'
            queue_item.save()

        # Mark task as completed
        task.status = 'completed'
        task.save()

        # Handle automation project publishing
        if article.associated_automation_project:
            logger.debug(f"Article is associated with an automation project. Attempting to publish.")
            automation_project: AutomationProject = article.associated_automation_project

            # Attempting to auto publish the article
            res = None
            status = "publish"
            if automation_project.auto_publish_state == 'draft':
                status = "draft"

            if 'wordpress' in automation_project.selected_integration_name and user.wordpress_integrations.exists():
                res = publish_article_to_wp(article, user, status, automation_project.selected_integration_unique_text_id)
            elif 'webflow' in automation_project.selected_integration_name and user.webflow_integrations.exists():
                res = publish_article_to_wf(article, user, automation_project.selected_integration_unique_text_id, status)
            elif 'wix' in automation_project.selected_integration_name and user.wix_integrations.exists():
                res = publish_article_to_wix(article, status, automation_project.selected_integration_unique_text_id)
            else:
                logger.error(f"Could not auto publish article {article_uid} because no integration is selected or the user has not connected the integration")

            if res:
                if res['status'] == "success":
                    logger.info(f"Article {article_uid} auto published to {automation_project.selected_integration_name}")
                elif res['error_message']:
                    logger.error(f"{res['error_message']}")
            else:
                logger.error(f"Could not auto publish article {article_uid} to {automation_project.selected_integration_name} because no integration is selected or the user has not connected the integration")

        # send email to user
        if user.send_notification_emails:
            email_message: str = article_generation_success_email_body(user.username, article_uid)
            subject: str = "Your Generated Article is Ready"
            send_email(user.email, ABUN_NOTIFICATION_EMAIL, "Team Abun", subject, email_message)

        # Delete the redis key
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.delete(job_id)

        return JsonResponse(status=200, data={'message': "OK"})

    # =========================================================================
    # ----------------------- INTERNAL LINKING - FAILED -----------------------
    # =========================================================================
    elif event_type == 'internal_linking_failed':
        error_message: str = request.data['error_message']
        logger.error(f"INTERNAL LINKING TASK FAILED: {error_message}")

        task.retry_attempts += 1

        if task.retry_attempts >= settings.K8_JOB_RETRIES:
            article_uid: str = task.metadata
            article = Article.objects.get(article_uid=article_uid)

            # Mark article as completed without internal links
            article.is_processing = False
            article.is_generated = True
            article.generated_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
            article.internal_link_count = 0
            article.save()

            # Update stats
            AllArticlesStats.objects.update_or_create(
                article_uid=article_uid,
                defaults={
                    'generated_on': datetime.datetime.now(tz=ZoneInfo('UTC')),
                    'is_successful': True  # Article is still successful, just without internal links
                }
            )

            if queue_item:
                # Mark queue item as failed
                queue_item.status = 'failed'
                queue_item.error_message = error_message
                queue_item.save()

            task.status = 'failed'
            task.fail_reason = f"{error_message}. All K8 retry attempts have been exhausted."

            # Delete the redis key
            with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                redis_connection.delete(job_id)

        task.save()
        return JsonResponse(status=200, data={'message': 'OK'})

    else:
        logger.exception(f"Kubernetes Job sent unrecognized 'event_type' {event_type} to internal link webhook")


@cors_allow_all
@api_view(['POST'])
def ai_calculator_webhook(request: Request) -> JsonResponse:
    """
    Unified webhook for AI calculator generation and modification kubernetes jobs.
    Determines if it's generation or modification based on existing conversation history.

    :param request: Django Rest Framework's Request object
    """
    event_type: str = request.data['event']
    job_id: str = request.data['job_id']

    logger.debug(event_type)
    logger.debug(job_id)

    try:
        task = KubernetesJob.objects.get(job_id=job_id)
        user: User = task.user
    except KubernetesJob.DoesNotExist:
        logger.error(f"Kubernetes Job {job_id} does not exists.")
        return JsonResponse(status=500, data={'message': "Could not find associated KubernetesJob. "
                                                         "Calculator or task might have been deleted"})

    # Get calculator_id from metadata
    calculator_id = task.metadata

    # ==========================================================================
    # ---------------------- CALCULATOR OPERATION - SUCCESS ----------------------
    # ==========================================================================
    if event_type == 'ai_calculator_completed':
        html_content: str = request.data['data']['html_content']

        # Optional fields that may be present for generation or modification
        calc_type: str = request.data['data']['calc_type']
        calc_description: str = request.data['data']['calc_description']
        modifications_instruction: str = request.data['data'].get('modifications_instruction')

        try:
            # Get the calculator instance
            calculator = AICalculator.objects.get(calculator_id=calculator_id)

            # Determine if this is generation or modification based on conversation history
            try:
                existing_conversation = json.loads(calculator.conversation) if calculator.conversation else []
            except (json.JSONDecodeError, TypeError):
                existing_conversation = []

            is_generation = len(existing_conversation) == 0

            # Apply branding based on user's plan
            try:
                current_plan_data = get_stripe_product_data(user)
                current_plan_name = current_plan_data['name']
            except:
                current_plan_name = "Trial"

            # Handle branding logic
            if current_plan_name == "Trial":
                if is_generation:
                    # For generation, always add branding
                    html_content = add_calculator_branding(html_content, calc_type)
                else:
                    # For modification, add branding if not present
                    if 'Made with' not in html_content or 'Abun.com' not in html_content:
                        html_content = add_calculator_branding(html_content, calculator.calc_type)
            else:
                # For paid plans, ensure branding is removed if present
                if 'Made with' in html_content and 'Abun.com' in html_content:
                    html_content = remove_calculator_branding(html_content)

            # Update conversation history
            if is_generation:
                # This is a new generation
                conversation_history = [{
                    "user": f"Calculator Type: {calc_type}\nCalculator Description: {calc_description}",
                    "ai": html_content
                }]
                logger.info(f"Calculator generation completed successfully for {calculator_id}")
            else:
                # This is a modification
                conversation_history = existing_conversation
                conversation_history.append({
                    "user": modifications_instruction,
                    "ai": html_content
                })
                logger.info(f"Calculator modification completed successfully for {calculator_id}")

            # Update calculator
            calculator.conversation = json.dumps(conversation_history)
            calculator.html_code = html_content  # This will create a new version
            calculator.generation_status = 'completed'
            calculator.save()

            # Update K8s job status
            task.status = 'completed'
            task.save()

        except AICalculator.DoesNotExist:
            logger.error(f"Calculator with ID {calculator_id} not found.")
            task.status = 'failed'
            task.fail_reason = f"Calculator with ID {calculator_id} not found."
            task.save()
            return JsonResponse(status=404, data={'message': f"Calculator with ID {calculator_id} not found."})

        return JsonResponse(status=200, data={'message': "OK"})

    # =========================================================================
    # ---------------------- CALCULATOR OPERATION - FAILED ----------------------
    # =========================================================================
    elif event_type == 'ai_calculator_failed':
        error_message: str = request.data['error_message']
        logger.error(f"CALCULATOR TASK FAILED: {error_message}")

        task.retry_attempts += 1

        if task.retry_attempts >= settings.K8_JOB_RETRIES:
            # Determine operation type for logging
            try:
                calculator = AICalculator.objects.get(calculator_id=calculator_id)
                existing_conversation = json.loads(calculator.conversation) if calculator.conversation else []
                operation_type = "generation" if len(existing_conversation) == 0 else "modification"

                # Update calculator generation_status to failed
                calculator.generation_status = 'failed'
                calculator.save()
            except:
                operation_type = "calculator operation"

            task.status = 'failed'
            task.fail_reason = f"Calculator {operation_type} failed after {settings.K8_JOB_RETRIES} retries: {error_message}"
            task.save()

            # Update the user's calculator count
            user.ai_calculators_generated -= 1
            user.total_ai_calculators_generated -= 1
            user.save()

            logger.error(f"Calculator {operation_type} failed for {calculator_id} after {settings.K8_JOB_RETRIES} retries: {error_message}")

        else:
            # Retry the task
            task.save()
            logger.info(f"Retrying calculator task {job_id}. Attempt {task.retry_attempts}/{settings.K8_JOB_RETRIES}")

        return JsonResponse(status=200, data={'message': 'OK'})

    else:
        logger.exception(f"Kubernetes Job sent unrecognized 'event_type' {event_type} to calculator webhook")
        return JsonResponse(status=501, data={'message': f"Unrecognized event type {event_type}"})
