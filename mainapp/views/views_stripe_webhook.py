import hashlib
import json
import logging
import time
import datetime
from typing import Dict, Optional

import stripe
import stripe.error
from django.http import HttpResponse
from django.utils import timezone
from rest_framework.decorators import api_view
from rest_framework.request import Request

from mainapp.decorators import cors_allow_all
from mainapp.models import User
from mainapp.stripe_utils import check_stripe_event_idempotency_key, save_idempotency_key, \
    generate_stripe_event_idempotency_key, get_stripe_product_data_by_name, get_stripe_product_data, cancel_trial_subscription_if_needed

logger = logging.getLogger('abun.stripe_webhook')


# NOTE: Using @transaction.atomic in the webhook code results in some weird behavior. So I removed them.

@cors_allow_all
@api_view(['POST'])
def stripe_webhook(request: Request) -> HttpResponse:
    """
    Stripe events webhook.

    :param request: Django Rest Framework's Request object
    """
    # NOTE: We are not verifying the signature here because DRF modifies the payload data on its end while stripe and
    # working around that is a major pain in the ass.
    payload = request.body
    event = json.loads(payload)

    idempotency_key: Optional[str] = event['request']['idempotency_key']
    if idempotency_key:
        if check_stripe_event_idempotency_key(idempotency_key):
            logger.info(f"Idempotency Key {idempotency_key} for event {event['type']} is present in Redis. "
                        f"Skipping event...")
            return HttpResponse(status=200)
    else:
        logger.info(f"Idempotency key was null for event {event['id']}")

    # --------------------- Handle the events ---------------------
    if event['type'] == 'customer.created':
        logger.debug("------------- CUSTOMER CREATED -------------")
        customer: Dict = event['data']['object']
        add_stripe_customer_id(customer, idempotency_key)

    elif event['type'] == 'customer.deleted':
        logger.debug("------------- CUSTOMER DELETED -------------")
        customer: Dict = event['data']['object']
        remove_stripe_customer_id(customer, idempotency_key)
        reset_user_subscription_data(customer['email'])

    elif event['type'] == 'invoice.paid':
        logger.debug("------------- INVOICE PAID -------------")
        invoice = event['data']['object']
        if not idempotency_key:
            # If idempotency_key is null, generate one from invoice id + customer id + event id
            h = hashlib.sha256()
            h.update(event['id'].encode('utf-8'))
            h.update(event['data']['object']['customer'].encode('utf-8'))
            h.update(event['data']['object']['id'].encode('utf-8'))
            idempotency_key = h.hexdigest()

        set_user_plan(invoice, idempotency_key)

    elif event['type'] == 'customer.subscription.deleted':
        logger.debug("------------- SUBSCRIPTION CANCELLED -------------")
        subscription = event['data']['object']
        if not idempotency_key:
            # If idempotency_key is null, generate one from subscription id + customer id + event id
            h = hashlib.sha256()
            h.update(event['id'].encode('utf-8'))
            h.update(event['data']['object']['customer'].encode('utf-8'))
            h.update(event['data']['object']['id'].encode('utf-8'))
            idempotency_key = h.hexdigest()

        move_user_to_free_plan(subscription, idempotency_key)

    else:
        logger.debug(f"Unhandled event type {event['type']}")

    return HttpResponse(status=200)


def add_stripe_customer_id(event_data: Dict, idempotency_key: str):
    """
    Saves / Updates stripe customer id in User model.

    :param event_data: Stripe's 'customer.created' event data object.
    :param idempotency_key: Idempotency Key provided by stripe in event request data.
    """
    user_email: str = event_data['email']
    stripe_customer_id: str = event_data['id']

    user: User = User.objects.get(email=user_email)
    user.stripe_customer_id = stripe_customer_id
    user.save()

    # Save idempotency key to avoid duplicate events
    save_idempotency_key(idempotency_key)

    # Add default address if not added
    customer = stripe.Customer.retrieve(stripe_customer_id)

    if customer.currency == 'inr' and not customer.address:
        default_address = {
            "city": f"'{user.email}' city",
            "country": "india",
            "line1": f"'{user.email}' address line1",
            "line2": f"'{user.email}' address line2",
            "postal_code": f"'{user.email}' postal code",
            "state": f"'{user.email}' state"
        }

        stripe.Customer.modify(stripe_customer_id, address=default_address)


def remove_stripe_customer_id(event_data: Dict, idempotency_key: str):
    """
    Removes stripe customer id from User model.

    :param event_data: Stripe's 'customer.deleted' event data object.
    :param idempotency_key: Idempotency Key provided by stripe in event request data.
    """
    user_email: str = event_data['email']

    if user_email:
        try:
            user: User = User.objects.get(email=user_email)
            user.stripe_customer_id = None
            user.stripe_pricing_id = None
            user.stripe_product_id = None
            user.stripe_subscription_id = None
            user.save()
        except User.DoesNotExist:
            logger.error(f"Unable to remove stripe customer id: No user with email id '{user_email}'")
    else:
        stripe_customer_id: str = event_data['id']
        try:
            user: User = User.objects.get(stripe_customer_id=stripe_customer_id)
            user.stripe_customer_id = None
            user.stripe_pricing_id = None
            user.stripe_product_id = None
            user.stripe_subscription_id = None
            user.save()
        except User.DoesNotExist:
            logger.error(f"Unable to remove stripe customer id: No user with customer id '{stripe_customer_id}'")

    # Save idempotency key to avoid duplicate events
    if idempotency_key:
        save_idempotency_key(idempotency_key)


def reset_user_subscription_data(user_email: str):
    """
    Resets all subscription related data like subscription id, current price id etc. except for stripe customer id.

    :param user_email: User account email id.
    :raises User.DoesNotExist: if user with given email id was not found in DB.
    """
    try:
        user: User = User.objects.get(email=user_email)
    except User.DoesNotExist:
        logger.info(f"User account with email {user_email} does not exists.")
        return

    user.stripe_subscription_id = None
    user.stripe_product_id = None
    user.stripe_pricing_id = None
    user.save()


def set_user_plan(invoice_paid_data: Dict, idempotency_key: str):
    """
    This function is used to set user account based on the new/existing plan values. Used for both new subscriptions
    and renewals.

    :param invoice_paid_data: Stripe's 'invoice.paid' event data object.
    :param idempotency_key: Idempotency Key provided by stripe in event request data.
    """
    trial_plan: Dict = get_stripe_product_data_by_name("Trial")
    customer_id: str = invoice_paid_data['customer']
    price_id: str = invoice_paid_data['lines']['data'][0]['price']['id']
    product_id: str = invoice_paid_data['lines']['data'][0]['price']['product']
    subscription_id: str = invoice_paid_data['subscription']

    try:
        user: User = User.objects.get(stripe_customer_id=customer_id)
    except User.DoesNotExist:
        logger.error(f"Stripe Webhook set_user_plan() - No user account with stripe id {customer_id} exists.")
        return HttpResponse(status=200)

    # We'll use this to check if user is currently on free tier. If so we'll cancel that plan at the end
    # This is for cases where user first purchased free plan.
    user_prev_product_id: str = user.stripe_product_id
    user_prev_sub_id: str = user.stripe_subscription_id

    # Set new subscription details
    user.stripe_subscription_id = subscription_id
    user.stripe_pricing_id = price_id
    user.stripe_product_id = product_id

    # Check if this is an annual plan and set next_billing_date
    try:
        # Retrieve the subscription to get current_period_end
        subscription = stripe.Subscription.retrieve(subscription_id)
        price = stripe.Price.retrieve(price_id)

        # Check if this is an annual plan by looking at the price lookup_key
        is_annual_plan = price.lookup_key and 'annual' in price.lookup_key.lower() or False

        if is_annual_plan:
            # For annual plans, set next_billing_date to current_period_end
            next_billing_timestamp = subscription['current_period_end']
            user.next_billing_date = datetime.datetime.fromtimestamp(
                next_billing_timestamp, tz=timezone.utc
            )
            logger.info(f"Set next_billing_date for annual plan user {user.email}: {user.next_billing_date}")
        else:
            # For monthly plans, clear the next_billing_date
            user.next_billing_date = None

    except Exception as e:
        logger.error(f"Error setting next_billing_date for user {user.email}: {str(e)}")
        # Don't fail the webhook if this fails
        pass

    # Reset affected attributes
    user.articles_generated = 0
    user.titles_generated = 0
    user.keywords_generated = 0
    user.blog_emails_found = 0
    user.ai_calculators_generated = 0
    user.automation_projects_generated = 0
    user.glossary_topic_generated = 0
    user.glossary_contents_generated = 0
    user.guest_post_finder_queries_generated = 0
    user.reddit_post_finder_queries_generated = 0

    # If this is not trial plan, mark account as verified
    if product_id != trial_plan['id']:
        user.verified = True

    user.save()

    # Cancel trial subscription if needed
    cancel_trial_subscription_if_needed(user, user_prev_product_id, user_prev_sub_id, trial_plan)

    if user_prev_product_id != subscription_id:
        # Update user featured image template ID based on plan selection
        try:
            plan_name = get_stripe_product_data(user)['name']
        except stripe.error.InvalidRequestError:
            plan_name = 'Trial'

        if plan_name == "Basic":
            user.feature_image_template_id = "ok0l2K5mppOLZ3j1Yx"
        elif plan_name == "Pro":
            user.feature_image_template_id = "yKBqAzZ9xwB0bvMx36"

        user.save()

    # Save idempotency key to avoid duplicate events
    save_idempotency_key(idempotency_key)


def move_user_to_free_plan(subscription: Dict, idempotency_key: str):
    """
    Creates new subscription with free plan. Please note this function does not cancel any existing plan. That should
    be handled before calling this function.

    :param subscription: Stripe subscription data.
    :param idempotency_key: Idempotency Key provided by stripe in event request data.
    :return:
    """
    trial_plan: Dict = get_stripe_product_data_by_name("Trial")
    stripe_customer_id: str = subscription['customer']
    deleted_price_id: str = subscription['items']['data'][0]['price']['id']

    try:
        user: User = User.objects.get(stripe_customer_id=stripe_customer_id)
    except User.DoesNotExist:
        logger.error(f"move_user_to_free_plan() - No user account with "
                     f"customer id {stripe_customer_id} exists.")
        return HttpResponse(status=200)

    if user.stripe_product_id == trial_plan['id']:
        # User is already on trial plan. Ignore this event then.
        logger.debug(f"User {user.email} is already on Trial plan")
        return

    if user.stripe_pricing_id != deleted_price_id:
        logger.debug(f"User {user.email} current price id ({user.stripe_pricing_id}) does not match the "
                     f"deleted price id ({deleted_price_id}). Ignoring event.")
        return

    mark_latest_invoice_uncollectible_if_open(stripe_customer_id)

    free_plan_subscription_ik = generate_stripe_event_idempotency_key(
        "free plan subscription",
        user.email,
        user.username
    )
    try:
        
        if user.country.lower() == "india":
            item_price = trial_plan["inr"]["id"]
        else:
            item_price = trial_plan["usd"]["id"]

        subscription = stripe.Subscription.create(
            customer=stripe_customer_id,
            items=[
                {'price': item_price}
            ],
            idempotency_key=free_plan_subscription_ik
        )
    except stripe.error.InvalidRequestError:
        # Request might fail in case this was triggered from customer deletion
        # In that case ignore it.
        logger.debug("move_user_to_free_plan() - Trial sub creation failed because user was deleted on stripe.")

    # Set new subscription details
    user.stripe_subscription_id = subscription['id']
    user.stripe_pricing_id = subscription['items']['data'][0]['price']['id']
    user.stripe_product_id = subscription['items']['data'][0]['price']['product']

    # Reset affected attributes
    user.articles_generated = 0
    user.titles_generated = 0
    user.keywords_generated = 0
    user.blog_emails_found = 0
    user.ai_calculators_generated = 0
    user.automation_projects_generated = 0
    user.glossary_topic_generated = 0
    user.glossary_contents_generated = 0
    user.guest_post_finder_queries_generated = 0
    user.reddit_post_finder_queries_generated = 0
    user.content_calendar_generated = 0
    user.search_console_insights_generated = 0
    user.fast_indexing_generated = 0

    user.save()

    # Save idempotency key to avoid duplicate events
    save_idempotency_key(idempotency_key)


def mark_latest_invoice_uncollectible_if_open(customer_id: str):
    """
    Marks latest invoice uncollectible if it's currently open

    :param customer_id: Stripe customer id.
    :return:
    """
    try:
        latest_invoice = stripe.Invoice.list(customer=customer_id)['data'][0]
    except IndexError:
        logger.debug(f"User {customer_id} does not have any invoice.")
        return

    if latest_invoice['status'] == "open":
        logger.debug(f"Latest invoice '{latest_invoice['id']}' of user '{customer_id}' is open. "
                     f"Marking it uncollectible...")
        stripe.Invoice.void_invoice(latest_invoice['id'])


def handle_payment_failure(invoice_data: Dict, idempotency_key: str):
    """
    Use this for invoice.payment_failed event. Behavior is as follows:

    - If user upgrade/downgrade fails, we immediately move them to their previous plan and void the invoices.
    - In case of renewals (new invoice proudct is same as current product on user account) we don't do anything.
    - In case of first time purchases, don't do anything.

    :param invoice_data: invoice.payment_failed event invoice data
    :param idempotency_key: Idempotency Key provided by stripe in event request data.
    :return:
    """
    inv = stripe.Invoice.retrieve(invoice_data['id'], expand=['payment_intent'])

    # Save idempotency key to avoid duplicate events
    if idempotency_key:
        save_idempotency_key(idempotency_key)

    # using customer email in case stripe customer id is not added yt
    customer_email: str = invoice_data['customer_email']

    try:
        user: User = User.objects.get(email=customer_email)
    except User.DoesNotExist:
        logger.error(f"Error in handle_payment_failure() - User account with email {customer_email} was not found.")
        return

    # get product id for which payment failed
    failed_product_id: str = invoice_data['lines']['data'][0]['price']['product']

    # --------------------- First purchase from stripe failed ---------------------
    if user.stripe_product_id is None:
        # Set user's subscription details back to NULL
        user.stripe_pricing_id = None
        user.stripe_product_id = None
        user.stripe_subscription_id = None
        user.save()

    # --------------------- Renewal failure ---------------------
    elif user.stripe_product_id == failed_product_id:
        # No need to do anything.
        return

    # --------------------- Failure during upgrade/downgrade ---------------------
    else:
        # Get the subscription and revert it to previous plan
        sub_id = invoice_data['subscription']
        sub = stripe.Subscription.retrieve(sub_id)
        stripe.Subscription.modify(
            sub_id,
            items=[{'id': sub['items']['data'][0]['id'], 'price': user.stripe_pricing_id}],
            proration_behavior='none'
        )
        # Void the latest invoice for failed payment
        time.sleep(2)
        updated_invoice_data = stripe.Invoice.retrieve(invoice_data['id'])
        if updated_invoice_data['status'] == 'open':
            stripe.Invoice.void_invoice(invoice_data['id'])
