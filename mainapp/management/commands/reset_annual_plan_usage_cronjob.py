import logging

import stripe
from dateutil.relativedelta import relativedelta

from django.utils import timezone
from django.core.management.base import BaseCommand

from mainapp.models import User


logger = logging.getLogger(__name__)

class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        current_time = timezone.now()
        logger.info(f"Starting annual plan usage counter reset at {current_time}")

        # Find users on annual plans whose next_billing_date has passed
        users = User.objects.filter(
            next_billing_date__isnull=False,
            next_billing_date__lte=current_time,
            stripe_subscription_id__isnull=False
        )

        reset_count = 0

        for user in users:
            try:
                logger.info(f"Processing user: {user.email}")

                # Verify the user still has an active subscription
                if not user.stripe_subscription_id:
                    logger.warning(f"User {user.email} has next_billing_date but no subscription ID. Skipping.")
                    continue

                # Get subscription details from Stripe
                try:
                    subscription = stripe.Subscription.retrieve(user.stripe_subscription_id)
                    if subscription['status'] not in ['active', 'trialing']:
                        logger.warning(f"User {user.email} subscription is not active (status: {subscription['status']}). Clearing next_billing_date.")
                        user.next_billing_date = None
                        user.save()
                        continue

                except stripe.error.InvalidRequestError:
                    logger.warning(f"User {user.email} subscription not found in Stripe. Clearing next_billing_date.")
                    user.next_billing_date = None
                    user.save()
                    continue

                # Verify this is still an annual plan
                try:
                    price = stripe.Price.retrieve(user.stripe_pricing_id)
                    is_annual_plan = price.lookup_key and 'annual' in price.lookup_key.lower()
                    
                    if not is_annual_plan:
                        logger.warning(f"User {user.email} is no longer on annual plan. Clearing next_billing_date.")
                        user.next_billing_date = None
                        user.save()
                        continue

                except stripe.error.InvalidRequestError:
                    logger.warning(f"User {user.email} price not found in Stripe. Clearing next_billing_date.")
                    user.next_billing_date = None
                    user.save()
                    continue

                # Store old values for logging
                old_values = {
                    'articles': user.articles_generated,
                    'keywords': user.keywords_generated,
                    'titles': user.titles_generated,
                    'email': user.blog_emails_found,
                    'glossary_topic': user.glossary_topic_generated,
                    'glossary_contents': user.glossary_contents_generated,
                    'guest_post_finder_queries': user.guest_post_finder_queries_generated,
                    'reddit_post_finder_queries': user.reddit_post_finder_queries_generated,
                    'ai_calculators': user.ai_calculators_generated,
                    'automation_projects': user.automation_projects_generated,
                    'old_billing_date': user.next_billing_date
                }

                # Reset counters
                user.articles_generated = 0
                user.keywords_generated = 0
                user.titles_generated = 0
                user.blog_emails_found = 0
                user.ai_calculators_generated = 0
                user.automation_projects_generated = 0
                user.glossary_topic_generated = 0
                user.glossary_contents_generated = 0
                user.guest_post_finder_queries_generated = 0
                user.reddit_post_finder_queries_generated = 0
                user.content_calendar_generated = 0
                user.search_console_insights_generated = 0
                user.fast_indexing_generated = 0

                # Update next_billing_date to the next billing cycle
                # For annual plans, add 1 year to current next_billing_date
                user.next_billing_date = user.next_billing_date + relativedelta(years=1)

                # Save changes
                user.save()

                reset_count += 1

                self.stdout.write(
                    f"Reset counters for annual plan user '{user.email}'.\n"
                    f"Old values - Articles: {old_values['articles']},\n"
                    f"Keywords: {old_values['keywords']},\n"
                    f"Titles: {old_values['titles']}.\n"
                    f"Emails: {old_values['email']}.\n"
                    f"Glossary Topic: {old_values['glossary_topic']}.\n"
                    f"Glossary Contents: {old_values['glossary_contents']}.\n"
                    f"Guest Post Finder Queries: {old_values['guest_post_finder_queries']}.\n"
                    f"Reddit Post Finder Queries: {old_values['reddit_post_finder_queries']}.\n"
                    f"AI Calculators: {old_values['ai_calculators']}.\n"
                    f"Automation Projects: {old_values['automation_projects']}.\n"
                    f"Next billing date updated from {old_values['old_billing_date']}\n"
                    f"to {user.next_billing_date}\n\n"
                )

            except Exception as e:
                logger.critical(f"Error processing user {user.email}: {str(e)}")

        self.stdout.write(
            self.style.SUCCESS(f'Successfully processed {reset_count} annual plan users')
        )
